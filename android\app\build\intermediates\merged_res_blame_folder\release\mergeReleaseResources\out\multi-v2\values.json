{"logs": [{"outputFile": "com.legal2025.yamy.app-mergeReleaseResources-73:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5813dff2fc04f128ab917c6cfbab969f\\transformed\\jetified-play-services-basement-18.4.0\\res\\values\\values.xml", "from": {"startLines": "4,7", "startColumns": "0,0", "startOffsets": "243,406", "endColumns": "63,166", "endOffsets": "306,572"}, "to": {"startLines": "370,419", "startColumns": "4,4", "startOffsets": "22111,26041", "endColumns": "67,166", "endOffsets": "22174,26203"}}, {"source": "D:\\20223\\2025\\legl92025\\android\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "56", "endOffsets": "107"}, "to": {"startLines": "108", "startColumns": "4", "startOffsets": "5573", "endColumns": "56", "endOffsets": "5625"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\305e40512bf13f0ccd5d12e0c8791b8d\\transformed\\jetified-play-services-base-18.1.0\\res\\values\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,33,46", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "215,301,377,463,549,625,702,778,951,1052,1233,1354,1457,1637,1756,1868,1967,2155,2256,2437,2558,2733,2877,2936,2994,3164,3475", "endLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,45,64", "endColumns": "85,75,85,85,75,76,75,75,100,180,120,102,179,118,111,98,187,100,180,120,174,143,58,57,74,20,20", "endOffsets": "300,376,462,548,624,701,777,853,1051,1232,1353,1456,1636,1755,1867,1966,2154,2255,2436,2557,2732,2876,2935,2993,3068,3474,3887"}, "to": {"startLines": "90,91,92,93,94,95,96,97,411,412,413,414,415,416,417,418,420,421,422,423,424,425,426,427,428,3132,3548", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4221,4311,4391,4481,4571,4651,4732,4812,25001,25106,25287,25412,25519,25699,25822,25938,26208,26396,26501,26682,26807,26982,27130,27193,27255,176026,189564", "endLines": "90,91,92,93,94,95,96,97,411,412,413,414,415,416,417,418,420,421,422,423,424,425,426,427,428,3144,3566", "endColumns": "89,79,89,89,79,80,79,79,104,180,124,106,179,122,115,102,187,104,180,124,174,147,62,61,78,20,20", "endOffsets": "4306,4386,4476,4566,4646,4727,4807,4887,25101,25282,25407,25514,25694,25817,25933,26036,26391,26496,26677,26802,26977,27125,27188,27250,27329,176336,189976"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\cb0350436104f0549bbfb54632372cde\\transformed\\recyclerview-1.0.0\\res\\values\\values.xml", "from": {"startLines": "30,31,32,33,34,35,36,2", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "1535,1594,1642,1698,1773,1849,1921,55", "endLines": "30,31,32,33,34,35,36,29", "endColumns": "58,47,55,74,75,71,65,24", "endOffsets": "1589,1637,1693,1768,1844,1916,1982,1530"}, "to": {"startLines": "234,235,236,244,245,246,325,3474", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "14114,14173,14221,14888,14963,15039,19753,186904", "endLines": "234,235,236,244,245,246,325,3493", "endColumns": "58,47,55,74,75,71,65,24", "endOffsets": "14168,14216,14272,14958,15034,15106,19814,187694"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\19501761fecbdb52c8e982479daffff6\\transformed\\jetified-core-common-2.0.3\\res\\values\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "166", "endLines": "11", "endColumns": "8", "endOffsets": "571"}, "to": {"startLines": "2025", "startColumns": "4", "startOffsets": "131737", "endLines": "2032", "endColumns": "8", "endOffsets": "132142"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b5a70d0ba7abaf74188a334ea68a3f12\\transformed\\jetified-credentials-play-services-auth-1.2.0-rc01\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "5", "endColumns": "12", "endOffsets": "273"}, "to": {"startLines": "2021", "startColumns": "4", "startOffsets": "131514", "endLines": "2024", "endColumns": "12", "endOffsets": "131732"}}, {"source": "D:\\20223\\2025\\legl92025\\android\\app\\src\\main\\res\\values\\styles.xml", "from": {"startLines": "3,14", "startColumns": "4,4", "startOffsets": "176,832", "endLines": "7,16", "endColumns": "12,12", "endOffsets": "483,998"}, "to": {"startLines": "1541,1545", "startColumns": "4,4", "startOffsets": "98665,98846", "endLines": "1544,1547", "endColumns": "12,12", "endOffsets": "98841,99010"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7be1c0bb4075e2006ba23d0329c62fdb\\transformed\\browser-1.8.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,113,179,242,304,375,447,515,582,661", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "108,174,237,299,370,442,510,577,656,725"}, "to": {"startLines": "82,83,84,85,223,224,430,433,434,435", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "3726,3784,3850,3913,13310,13381,27372,27641,27708,27787", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "3779,3845,3908,3970,13376,13448,27435,27703,27782,27851"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e53585da63edf0e2fa4a6374876313c7\\transformed\\appcompat-1.1.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,54,55,56,57,58,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,218,219,223,227,231,236,242,249,253,257,262,266,270,274,278,282,286,292,296,302,306,312,316,321,325,328,332,338,342,348,352,358,361,365,369,373,377,381,382,383,384,387,390,393,396,400,401,402,403,404,407,409,411,413,418,419,423,429,433,434,436,447,448,452,458,462,463,464,468,495,499,500,504,532,703,729,901,927,958,966,972,986,1008,1013,1018,1028,1037,1046,1050,1057,1065,1072,1073,1082,1085,1088,1092,1096,1100,1103,1104,1109,1114,1124,1129,1136,1142,1143,1146,1150,1155,1157,1159,1162,1165,1167,1171,1174,1181,1184,1187,1191,1193,1197,1199,1201,1203,1207,1215,1223,1235,1241,1250,1253,1264,1267,1268,1273,1274,1279,1348,1418,1419,1429,1438,1439,1441,1445,1448,1451,1454,1457,1460,1463,1466,1470,1473,1476,1479,1483,1486,1490,1494,1495,1496,1497,1498,1499,1500,1501,1502,1503,1504,1505,1506,1507,1508,1509,1510,1511,1512,1513,1514,1516,1518,1519,1520,1521,1522,1523,1524,1525,1527,1528,1530,1531,1533,1535,1536,1538,1539,1540,1541,1542,1543,1545,1546,1547,1548,1549,1550,1552,1554,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1566,1567,1568,1570,1571,1572,1573,1574,1575,1577,1581,1585,1586,1587,1588,1589,1590,1594,1595,1596,1597,1599,1601,1603,1605,1607,1608,1609,1610,1612,1614,1616,1617,1618,1619,1620,1621,1622,1623,1624,1625,1626,1627,1630,1631,1632,1633,1635,1637,1638,1640,1641,1643,1645,1647,1648,1649,1650,1651,1652,1653,1654,1655,1656,1657,1658,1660,1661,1662,1663,1665,1666,1667,1668,1669,1671,1673,1675,1677,1678,1679,1680,1681,1682,1683,1684,1685,1686,1687,1688,1689,1690,1691,1692,1775,1778,1781,1784,1798,1809,1819,1849,1876,1885,1960,2357,2362,2390,2408,2444,2450,2456,2479,2620,2640,2646,2650,2656,2693,2705,2771,2795,2864,2883,2909", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,160,205,254,295,350,409,471,552,613,688,764,841,919,1004,1086,1162,1238,1315,1393,1499,1605,1684,1764,1821,1879,1953,2028,2093,2159,2219,2280,2352,2425,2492,2560,2619,2678,2737,2796,2855,2909,2963,3016,3070,3124,3178,3232,3306,3385,3458,3603,3675,3747,3820,3877,4008,4082,4156,4231,4303,4376,4446,4517,4577,4638,4707,4776,4846,4920,4996,5060,5137,5213,5290,5355,5424,5501,5576,5645,5713,5790,5856,5917,6014,6079,6148,6247,6318,6377,6435,6492,6551,6615,6686,6758,6830,6902,6974,7041,7109,7177,7236,7299,7363,7453,7544,7604,7670,7737,7803,7873,7937,7990,8057,8118,8185,8298,8356,8419,8484,8549,8624,8697,8769,8818,8879,8940,9001,9063,9127,9191,9255,9320,9383,9443,9504,9570,9629,9689,9751,9822,9882,9950,10036,10123,10213,10300,10388,10470,10553,10643,10734,10786,10844,10889,10955,11019,11076,11133,11187,11244,11292,11341,11392,11426,11473,11522,11568,11600,11664,11786,11843,11917,11987,12065,12119,12189,12274,12322,12368,12429,12492,12558,12622,12693,12756,12821,12885,12946,13007,13059,13132,13206,13275,13350,13424,13498,13639,13709,13762,13840,13930,14018,14114,14204,14786,14875,15122,15403,15655,15940,16333,16810,17032,17254,17530,17757,17987,18217,18447,18677,18904,19323,19549,19974,20204,20632,20851,21134,21342,21473,21700,22126,22351,22778,22999,23424,23544,23820,24121,24445,24736,25050,25187,25318,25423,25665,25832,26036,26244,26515,26627,26739,26844,26961,27175,27321,27461,27547,27895,27983,28229,28647,28896,28978,29076,29693,29793,30045,30469,30724,30818,30907,31144,33196,33438,33540,33793,35977,47101,48617,59840,61368,63125,63751,64171,65232,66497,66753,66989,67536,68030,68635,68833,69413,69977,70352,70470,71008,71165,71361,71634,71890,72060,72201,72265,72630,72997,73673,73937,74275,74628,74722,74908,75214,75476,75601,75728,75967,76178,76297,76490,76667,77122,77303,77425,77684,77797,77984,78086,78193,78322,78597,79105,79601,80478,80772,81342,81491,82223,82395,82479,82815,82907,83185,88594,94146,94208,94838,95452,95543,95656,95885,96045,96197,96368,96534,96703,96870,97033,97276,97446,97619,97790,98064,98263,98468,98798,98882,98978,99074,99172,99272,99374,99476,99578,99680,99782,99882,99978,100090,100219,100342,100473,100604,100702,100816,100910,101050,101184,101280,101392,101492,101608,101704,101816,101916,102056,102192,102356,102486,102644,102794,102935,103079,103214,103326,103476,103604,103732,103868,104000,104130,104260,104372,104512,104658,104802,104940,105006,105096,105172,105276,105366,105468,105576,105684,105784,105864,105956,106054,106164,106242,106348,106440,106544,106654,106776,106939,107096,107176,107276,107366,107476,107566,107807,107901,108007,108099,108199,108311,108425,108541,108657,108751,108865,108977,109079,109199,109321,109403,109507,109627,109753,109851,109945,110033,110145,110261,110383,110495,110670,110786,110872,110964,111076,111200,111267,111393,111461,111589,111733,111861,111930,112025,112140,112253,112352,112461,112572,112683,112784,112889,112989,113119,113210,113333,113427,113539,113625,113729,113825,113913,114031,114135,114239,114365,114453,114561,114661,114751,114861,114945,115047,115131,115185,115249,115355,115441,115551,115635,115755,120899,121017,121132,121264,121979,122671,123188,124787,126320,126708,131443,151705,151965,153475,154508,156521,156783,157139,157969,164751,165885,166179,166402,166729,168779,169427,173278,174480,178559,179774,181183", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,54,55,56,57,58,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,217,218,222,226,230,235,241,248,252,256,261,265,269,273,277,281,285,291,295,301,305,311,315,320,324,327,331,337,341,347,351,357,360,364,368,372,376,380,381,382,383,386,389,392,395,399,400,401,402,403,406,408,410,412,417,418,422,428,432,433,435,446,447,451,457,461,462,463,467,494,498,499,503,531,702,728,900,926,957,965,971,985,1007,1012,1017,1027,1036,1045,1049,1056,1064,1071,1072,1081,1084,1087,1091,1095,1099,1102,1103,1108,1113,1123,1128,1135,1141,1142,1145,1149,1154,1156,1158,1161,1164,1166,1170,1173,1180,1183,1186,1190,1192,1196,1198,1200,1202,1206,1214,1222,1234,1240,1249,1252,1263,1266,1267,1272,1273,1278,1347,1417,1418,1428,1437,1438,1440,1444,1447,1450,1453,1456,1459,1462,1465,1469,1472,1475,1478,1482,1485,1489,1493,1494,1495,1496,1497,1498,1499,1500,1501,1502,1503,1504,1505,1506,1507,1508,1509,1510,1511,1512,1513,1515,1517,1518,1519,1520,1521,1522,1523,1524,1526,1527,1529,1530,1532,1534,1535,1537,1538,1539,1540,1541,1542,1544,1545,1546,1547,1548,1549,1551,1553,1555,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1566,1567,1569,1570,1571,1572,1573,1574,1576,1580,1584,1585,1586,1587,1588,1589,1593,1594,1595,1596,1598,1600,1602,1604,1606,1607,1608,1609,1611,1613,1615,1616,1617,1618,1619,1620,1621,1622,1623,1624,1625,1626,1629,1630,1631,1632,1634,1636,1637,1639,1640,1642,1644,1646,1647,1648,1649,1650,1651,1652,1653,1654,1655,1656,1657,1659,1660,1661,1662,1664,1665,1666,1667,1668,1670,1672,1674,1676,1677,1678,1679,1680,1681,1682,1683,1684,1685,1686,1687,1688,1689,1690,1691,1774,1777,1780,1783,1797,1808,1818,1848,1875,1884,1959,2356,2361,2389,2407,2443,2449,2455,2478,2619,2639,2645,2649,2655,2692,2704,2770,2794,2863,2882,2908,2917", "endColumns": "54,44,48,40,54,58,61,80,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,71,71,72,56,57,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "155,200,249,290,345,404,466,547,608,683,759,836,914,999,1081,1157,1233,1310,1388,1494,1600,1679,1759,1816,1874,1948,2023,2088,2154,2214,2275,2347,2420,2487,2555,2614,2673,2732,2791,2850,2904,2958,3011,3065,3119,3173,3227,3301,3380,3453,3527,3670,3742,3815,3872,3930,4077,4151,4226,4298,4371,4441,4512,4572,4633,4702,4771,4841,4915,4991,5055,5132,5208,5285,5350,5419,5496,5571,5640,5708,5785,5851,5912,6009,6074,6143,6242,6313,6372,6430,6487,6546,6610,6681,6753,6825,6897,6969,7036,7104,7172,7231,7294,7358,7448,7539,7599,7665,7732,7798,7868,7932,7985,8052,8113,8180,8293,8351,8414,8479,8544,8619,8692,8764,8813,8874,8935,8996,9058,9122,9186,9250,9315,9378,9438,9499,9565,9624,9684,9746,9817,9877,9945,10031,10118,10208,10295,10383,10465,10548,10638,10729,10781,10839,10884,10950,11014,11071,11128,11182,11239,11287,11336,11387,11421,11468,11517,11563,11595,11659,11721,11838,11912,11982,12060,12114,12184,12269,12317,12363,12424,12487,12553,12617,12688,12751,12816,12880,12941,13002,13054,13127,13201,13270,13345,13419,13493,13634,13704,13757,13835,13925,14013,14109,14199,14781,14870,15117,15398,15650,15935,16328,16805,17027,17249,17525,17752,17982,18212,18442,18672,18899,19318,19544,19969,20199,20627,20846,21129,21337,21468,21695,22121,22346,22773,22994,23419,23539,23815,24116,24440,24731,25045,25182,25313,25418,25660,25827,26031,26239,26510,26622,26734,26839,26956,27170,27316,27456,27542,27890,27978,28224,28642,28891,28973,29071,29688,29788,30040,30464,30719,30813,30902,31139,33191,33433,33535,33788,35972,47096,48612,59835,61363,63120,63746,64166,65227,66492,66748,66984,67531,68025,68630,68828,69408,69972,70347,70465,71003,71160,71356,71629,71885,72055,72196,72260,72625,72992,73668,73932,74270,74623,74717,74903,75209,75471,75596,75723,75962,76173,76292,76485,76662,77117,77298,77420,77679,77792,77979,78081,78188,78317,78592,79100,79596,80473,80767,81337,81486,82218,82390,82474,82810,82902,83180,88589,94141,94203,94833,95447,95538,95651,95880,96040,96192,96363,96529,96698,96865,97028,97271,97441,97614,97785,98059,98258,98463,98793,98877,98973,99069,99167,99267,99369,99471,99573,99675,99777,99877,99973,100085,100214,100337,100468,100599,100697,100811,100905,101045,101179,101275,101387,101487,101603,101699,101811,101911,102051,102187,102351,102481,102639,102789,102930,103074,103209,103321,103471,103599,103727,103863,103995,104125,104255,104367,104507,104653,104797,104935,105001,105091,105167,105271,105361,105463,105571,105679,105779,105859,105951,106049,106159,106237,106343,106435,106539,106649,106771,106934,107091,107171,107271,107361,107471,107561,107802,107896,108002,108094,108194,108306,108420,108536,108652,108746,108860,108972,109074,109194,109316,109398,109502,109622,109748,109846,109940,110028,110140,110256,110378,110490,110665,110781,110867,110959,111071,111195,111262,111388,111456,111584,111728,111856,111925,112020,112135,112248,112347,112456,112567,112678,112779,112884,112984,113114,113205,113328,113422,113534,113620,113724,113820,113908,114026,114130,114234,114360,114448,114556,114656,114746,114856,114940,115042,115126,115180,115244,115350,115436,115546,115630,115750,120894,121012,121127,121259,121974,122666,123183,124782,126315,126703,131438,151700,151960,153470,154503,156516,156778,157134,157964,164746,165880,166174,166397,166724,168774,169422,173273,174475,178554,179769,181178,181652"}, "to": {"startLines": "4,27,28,59,60,61,62,64,65,66,67,68,69,72,73,74,75,76,77,78,79,80,81,86,87,98,99,100,101,102,103,104,105,106,107,109,110,111,112,113,114,115,116,117,118,119,120,125,126,127,128,130,131,132,133,134,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,232,233,237,238,239,240,241,242,243,273,274,275,276,277,278,279,280,316,317,318,319,324,332,333,338,360,366,367,369,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,447,452,453,454,455,456,457,465,466,470,474,478,483,489,496,500,504,509,513,517,521,525,529,533,539,543,549,553,559,563,568,572,575,579,585,589,595,599,605,608,612,616,620,624,628,629,630,631,634,637,640,643,647,648,649,650,651,654,656,658,660,665,666,670,676,680,681,683,694,695,699,705,709,710,711,715,742,746,747,751,779,949,975,1146,1172,1203,1211,1217,1231,1253,1258,1263,1273,1282,1291,1295,1302,1310,1317,1318,1327,1330,1333,1337,1341,1345,1348,1349,1354,1359,1369,1374,1381,1387,1388,1391,1395,1400,1402,1404,1407,1410,1412,1416,1419,1426,1429,1432,1436,1438,1442,1444,1446,1448,1452,1460,1468,1480,1486,1495,1498,1509,1512,1513,1518,1519,1548,1617,1687,1688,1698,1707,1859,1861,1865,1868,1871,1874,1877,1880,1883,1886,1890,1893,1896,1899,1903,1906,1910,1914,1915,1916,1917,1918,1919,1920,1921,1922,1923,1924,1925,1926,1927,1928,1929,1930,1931,1932,1933,1934,1936,1938,1939,1940,1941,1942,1943,1944,1945,1947,1948,1950,1951,1953,1955,1956,1958,1959,1960,1961,1962,1963,1965,1966,1967,1968,1969,1986,1988,1990,1992,1993,1994,1995,1996,1997,1998,1999,2000,2001,2002,2003,2004,2006,2007,2008,2009,2010,2011,2013,2017,2033,2034,2035,2036,2037,2038,2042,2043,2044,2045,2047,2049,2051,2053,2055,2056,2057,2058,2060,2062,2064,2065,2066,2067,2068,2069,2070,2071,2072,2073,2074,2075,2078,2079,2080,2081,2083,2085,2086,2088,2089,2091,2093,2095,2096,2097,2098,2099,2100,2101,2102,2103,2104,2105,2106,2108,2109,2110,2111,2113,2114,2115,2116,2117,2119,2121,2123,2125,2126,2127,2128,2129,2130,2131,2132,2133,2134,2135,2136,2137,2138,2139,2145,2220,2223,2226,2229,2243,2260,2302,2331,2358,2367,2429,2793,2824,2962,3086,3110,3116,3145,3166,3290,3318,3324,3468,3500,3567,3638,3738,3758,3813,3825,3851", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "225,891,936,1983,2024,2079,2138,2273,2354,2415,2490,2566,2643,2881,2966,3048,3124,3200,3277,3355,3461,3567,3646,3975,4032,4892,4966,5041,5106,5172,5232,5293,5365,5438,5505,5630,5689,5748,5807,5866,5925,5979,6033,6086,6140,6194,6248,6592,6666,6745,6818,6963,7035,7107,7180,7237,7368,7442,7516,7591,7663,7736,7806,7877,7937,7998,8067,8136,8206,8280,8356,8420,8497,8573,8650,8715,8784,8861,8936,9005,9073,9150,9216,9277,9374,9439,9508,9607,9678,9737,9795,9852,9911,9975,10046,10118,10190,10262,10334,10401,10469,10537,10596,10659,10723,10813,10904,10964,11030,11097,11163,11233,11297,11350,11417,11478,11545,11658,11716,11779,11844,11909,11984,12057,12129,12178,12239,12300,12361,12423,12487,12551,12615,12680,12743,12803,12864,12930,12989,13049,13111,13182,13242,13941,14027,14277,14367,14454,14542,14624,14707,14797,16734,16786,16844,16889,16955,17019,17076,17133,19310,19367,19415,19464,19719,20089,20136,20394,21565,21868,21932,22054,22375,22449,22519,22597,22651,22721,22806,22854,22900,22961,23024,23090,23154,23225,23288,23353,23417,23478,23539,23591,23664,23738,23807,23882,23956,24030,24171,28708,29069,29147,29237,29325,29421,29511,30093,30182,30429,30710,30962,31247,31640,32117,32339,32561,32837,33064,33294,33524,33754,33984,34211,34630,34856,35281,35511,35939,36158,36441,36649,36780,37007,37433,37658,38085,38306,38731,38851,39127,39428,39752,40043,40357,40494,40625,40730,40972,41139,41343,41551,41822,41934,42046,42151,42268,42482,42628,42768,42854,43202,43290,43536,43954,44203,44285,44383,44975,45075,45327,45751,46006,46100,46189,46426,48450,48692,48794,49047,51203,61735,63251,73882,75410,77167,77793,78213,79274,80539,80795,81031,81578,82072,82677,82875,83455,84019,84394,84512,85050,85207,85403,85676,85932,86102,86243,86307,86672,87039,87715,87979,88317,88670,88764,88950,89256,89518,89643,89770,90009,90220,90339,90532,90709,91164,91345,91467,91726,91839,92026,92128,92235,92364,92639,93147,93643,94520,94814,95384,95533,96265,96437,96521,96857,96949,99015,104261,109650,109712,110290,110874,118821,118934,119163,119323,119475,119646,119812,119981,120148,120311,120554,120724,120897,121068,121342,121541,121746,122076,122160,122256,122352,122450,122550,122652,122754,122856,122958,123060,123160,123256,123368,123497,123620,123751,123882,123980,124094,124188,124328,124462,124558,124670,124770,124886,124982,125094,125194,125334,125470,125634,125764,125922,126072,126213,126357,126492,126604,126754,126882,127010,127146,127278,127408,127538,127650,128930,129076,129220,129358,129424,129514,129590,129694,129784,129886,129994,130102,130202,130282,130374,130472,130582,130660,130766,130858,130962,131072,131194,131357,132147,132227,132327,132417,132527,132617,132858,132952,133058,133150,133250,133362,133476,133592,133708,133802,133916,134028,134130,134250,134372,134454,134558,134678,134804,134902,134996,135084,135196,135312,135434,135546,135721,135837,135923,136015,136127,136251,136318,136444,136512,136640,136784,136912,136981,137076,137191,137304,137403,137512,137623,137734,137835,137940,138040,138170,138261,138384,138478,138590,138676,138780,138876,138964,139082,139186,139290,139416,139504,139612,139712,139802,139912,139996,140098,140182,140236,140300,140406,140492,140602,140686,141090,143706,143824,143939,144019,144380,144966,146370,147714,149075,149463,152238,162327,163367,170180,174481,175232,175494,176341,176720,180998,181852,182081,186689,188029,189981,192381,196505,197249,199380,199720,201031", "endLines": "4,27,28,59,60,61,62,64,65,66,67,68,69,72,73,74,75,76,77,78,79,80,81,86,87,98,99,100,101,102,103,104,105,106,107,109,110,111,112,113,114,115,116,117,118,119,120,125,126,127,128,130,131,132,133,134,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,232,233,237,238,239,240,241,242,243,273,274,275,276,277,278,279,280,316,317,318,319,324,332,333,338,360,366,367,369,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,447,452,453,454,455,456,464,465,469,473,477,482,488,495,499,503,508,512,516,520,524,528,532,538,542,548,552,558,562,567,571,574,578,584,588,594,598,604,607,611,615,619,623,627,628,629,630,633,636,639,642,646,647,648,649,650,653,655,657,659,664,665,669,675,679,680,682,693,694,698,704,708,709,710,714,741,745,746,750,778,948,974,1145,1171,1202,1210,1216,1230,1252,1257,1262,1272,1281,1290,1294,1301,1309,1316,1317,1326,1329,1332,1336,1340,1344,1347,1348,1353,1358,1368,1373,1380,1386,1387,1390,1394,1399,1401,1403,1406,1409,1411,1415,1418,1425,1428,1431,1435,1437,1441,1443,1445,1447,1451,1459,1467,1479,1485,1494,1497,1508,1511,1512,1517,1518,1523,1616,1686,1687,1697,1706,1707,1860,1864,1867,1870,1873,1876,1879,1882,1885,1889,1892,1895,1898,1902,1905,1909,1913,1914,1915,1916,1917,1918,1919,1920,1921,1922,1923,1924,1925,1926,1927,1928,1929,1930,1931,1932,1933,1935,1937,1938,1939,1940,1941,1942,1943,1944,1946,1947,1949,1950,1952,1954,1955,1957,1958,1959,1960,1961,1962,1964,1965,1966,1967,1968,1969,1987,1989,1991,1992,1993,1994,1995,1996,1997,1998,1999,2000,2001,2002,2003,2005,2006,2007,2008,2009,2010,2012,2016,2020,2033,2034,2035,2036,2037,2041,2042,2043,2044,2046,2048,2050,2052,2054,2055,2056,2057,2059,2061,2063,2064,2065,2066,2067,2068,2069,2070,2071,2072,2073,2074,2077,2078,2079,2080,2082,2084,2085,2087,2088,2090,2092,2094,2095,2096,2097,2098,2099,2100,2101,2102,2103,2104,2105,2107,2108,2109,2110,2112,2113,2114,2115,2116,2118,2120,2122,2124,2125,2126,2127,2128,2129,2130,2131,2132,2133,2134,2135,2136,2137,2138,2139,2219,2222,2225,2228,2242,2248,2269,2330,2357,2366,2428,2787,2796,2851,2979,3109,3115,3121,3165,3289,3309,3323,3327,3473,3534,3578,3703,3757,3812,3824,3850,3857", "endColumns": "54,44,48,40,54,58,61,80,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,71,71,72,56,57,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "275,931,980,2019,2074,2133,2195,2349,2410,2485,2561,2638,2716,2961,3043,3119,3195,3272,3350,3456,3562,3641,3721,4027,4085,4961,5036,5101,5167,5227,5288,5360,5433,5500,5568,5684,5743,5802,5861,5920,5974,6028,6081,6135,6189,6243,6297,6661,6740,6813,6887,7030,7102,7175,7232,7290,7437,7511,7586,7658,7731,7801,7872,7932,7993,8062,8131,8201,8275,8351,8415,8492,8568,8645,8710,8779,8856,8931,9000,9068,9145,9211,9272,9369,9434,9503,9602,9673,9732,9790,9847,9906,9970,10041,10113,10185,10257,10329,10396,10464,10532,10591,10654,10718,10808,10899,10959,11025,11092,11158,11228,11292,11345,11412,11473,11540,11653,11711,11774,11839,11904,11979,12052,12124,12173,12234,12295,12356,12418,12482,12546,12610,12675,12738,12798,12859,12925,12984,13044,13106,13177,13237,13305,14022,14109,14362,14449,14537,14619,14702,14792,14883,16781,16839,16884,16950,17014,17071,17128,17182,19362,19410,19459,19510,19748,20131,20180,20435,21592,21927,21989,22106,22444,22514,22592,22646,22716,22801,22849,22895,22956,23019,23085,23149,23220,23283,23348,23412,23473,23534,23586,23659,23733,23802,23877,23951,24025,24166,24236,28756,29142,29232,29320,29416,29506,30088,30177,30424,30705,30957,31242,31635,32112,32334,32556,32832,33059,33289,33519,33749,33979,34206,34625,34851,35276,35506,35934,36153,36436,36644,36775,37002,37428,37653,38080,38301,38726,38846,39122,39423,39747,40038,40352,40489,40620,40725,40967,41134,41338,41546,41817,41929,42041,42146,42263,42477,42623,42763,42849,43197,43285,43531,43949,44198,44280,44378,44970,45070,45322,45746,46001,46095,46184,46421,48445,48687,48789,49042,51198,61730,63246,73877,75405,77162,77788,78208,79269,80534,80790,81026,81573,82067,82672,82870,83450,84014,84389,84507,85045,85202,85398,85671,85927,86097,86238,86302,86667,87034,87710,87974,88312,88665,88759,88945,89251,89513,89638,89765,90004,90215,90334,90527,90704,91159,91340,91462,91721,91834,92021,92123,92230,92359,92634,93142,93638,94515,94809,95379,95528,96260,96432,96516,96852,96944,97222,104256,109645,109707,110285,110869,110960,118929,119158,119318,119470,119641,119807,119976,120143,120306,120549,120719,120892,121063,121337,121536,121741,122071,122155,122251,122347,122445,122545,122647,122749,122851,122953,123055,123155,123251,123363,123492,123615,123746,123877,123975,124089,124183,124323,124457,124553,124665,124765,124881,124977,125089,125189,125329,125465,125629,125759,125917,126067,126208,126352,126487,126599,126749,126877,127005,127141,127273,127403,127533,127645,127785,129071,129215,129353,129419,129509,129585,129689,129779,129881,129989,130097,130197,130277,130369,130467,130577,130655,130761,130853,130957,131067,131189,131352,131509,132222,132322,132412,132522,132612,132853,132947,133053,133145,133245,133357,133471,133587,133703,133797,133911,134023,134125,134245,134367,134449,134553,134673,134799,134897,134991,135079,135191,135307,135429,135541,135716,135832,135918,136010,136122,136246,136313,136439,136507,136635,136779,136907,136976,137071,137186,137299,137398,137507,137618,137729,137830,137935,138035,138165,138256,138379,138473,138585,138671,138775,138871,138959,139077,139181,139285,139411,139499,139607,139707,139797,139907,139991,140093,140177,140231,140295,140401,140487,140597,140681,140801,143701,143819,143934,144014,144375,144608,145478,147709,149070,149458,152233,162137,162457,164719,170747,175227,175489,175689,176715,180993,181599,182076,182227,186899,189107,190288,195402,197244,199375,199715,201026,201229"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1214412e3b6f657b0a3026c46d581630\\transformed\\jetified-credentials-1.2.0-rc01\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,137", "endColumns": "81,83", "endOffsets": "132,216"}, "to": {"startLines": "401,402", "startColumns": "4,4", "startOffsets": "24241,24323", "endColumns": "81,83", "endOffsets": "24318,24402"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\34feee06b8e6a00411fec246d529fa0e\\transformed\\transition-1.4.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,95,142,185,240,287,341,393,442,503", "endColumns": "39,46,42,54,46,53,51,48,60,49", "endOffsets": "90,137,180,235,282,336,388,437,498,548"}, "to": {"startLines": "322,323,328,335,336,355,356,357,358,359", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "19632,19672,19889,20227,20282,21299,21353,21405,21454,21515", "endColumns": "39,46,42,54,46,53,51,48,60,49", "endOffsets": "19667,19714,19927,20277,20324,21348,21400,21449,21510,21560"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\76aeefdcdc1717e276ef95ad21040026\\transformed\\jetified-startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "403", "startColumns": "4", "startOffsets": "24407", "endColumns": "82", "endOffsets": "24485"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f38bbe38c62130c874a7fedc0088d8db\\transformed\\jetified-savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "363", "startColumns": "4", "startOffsets": "21700", "endColumns": "53", "endOffsets": "21749"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\46be280a5daaf9ae4cb2602a644b6ed7\\transformed\\coordinatorlayout-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,102,3,13", "startColumns": "4,4,4,4", "startOffsets": "55,5935,116,724", "endLines": "2,104,12,101", "endColumns": "60,12,24,24", "endOffsets": "111,6075,719,5930"}, "to": {"startLines": "3,2142,2852,2858", "startColumns": "4,4,4,4", "startOffsets": "164,140945,164724,164935", "endLines": "3,2144,2857,2941", "endColumns": "60,12,24,24", "endOffsets": "220,141085,164930,169446"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1bee42a6a1ac63167d59f2b6460e0e34\\transformed\\lifecycle-viewmodel-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "364", "startColumns": "4", "startOffsets": "21754", "endColumns": "49", "endOffsets": "21799"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\640fc94d5e11f299ffe0019b1ad17aa3\\transformed\\jetified-appcompat-resources-1.1.0\\res\\values\\values.xml", "from": {"startLines": "2,29,36,47,74", "startColumns": "4,4,4,4,4", "startOffsets": "55,1702,2087,2684,4317", "endLines": "28,35,46,73,78", "endColumns": "24,24,24,24,24", "endOffsets": "1697,2082,2679,4312,4582"}, "to": {"startLines": "2270,2286,2292,3618,3634", "startColumns": "4,4,4,4,4", "startOffsets": "145483,145908,146086,191843,192254", "endLines": "2285,2291,2301,3633,3637", "endColumns": "24,24,24,24,24", "endOffsets": "145903,146081,146365,192249,192376"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\13f2593a09a7e676e6a4e2c9d70ef7ac\\transformed\\core-1.13.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,155,176,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,2217,2276,2335,2395,2455,2515,2575,2635,2695,2755,2815,2875,2935,2994,3054,3114,3174,3234,3294,3354,3414,3474,3534,3594,3653,3713,3773,3832,3891,3950,4009,4068,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8840,9521,10203", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,154,175,208,214", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,2271,2330,2390,2450,2510,2570,2630,2690,2750,2810,2870,2930,2989,3049,3109,3169,3229,3289,3349,3409,3469,3529,3589,3648,3708,3768,3827,3886,3945,4004,4063,4122,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8835,9516,10198,10365"}, "to": {"startLines": "29,70,71,88,89,121,122,225,226,227,228,229,230,231,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,326,327,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,373,404,405,406,407,408,409,410,448,1970,1971,1976,1979,1984,2140,2141,2797,2814,2984,3017,3047,3080", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "985,2721,2793,4090,4155,6302,6371,13453,13523,13591,13663,13733,13794,13868,15111,15172,15233,15295,15359,15421,15482,15550,15650,15710,15776,15849,15918,15975,16027,17187,17259,17335,17400,17459,17518,17578,17638,17698,17758,17818,17878,17938,17998,18058,18118,18177,18237,18297,18357,18417,18477,18537,18597,18657,18717,18777,18836,18896,18956,19015,19074,19133,19192,19251,19819,19854,20440,20495,20558,20613,20671,20729,20790,20853,20910,20961,21011,21072,21129,21195,21229,21264,22305,24490,24557,24629,24698,24767,24841,24913,28761,127790,127907,128174,128467,128734,140806,140878,162462,163066,170901,172632,173632,174314", "endLines": "29,70,71,88,89,121,122,225,226,227,228,229,230,231,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,326,327,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,373,404,405,406,407,408,409,410,448,1970,1974,1976,1982,1984,2140,2141,2802,2823,3016,3037,3079,3085", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "1040,2788,2876,4150,4216,6366,6429,13518,13586,13658,13728,13789,13863,13936,15167,15228,15290,15354,15416,15477,15545,15645,15705,15771,15844,15913,15970,16022,16084,17254,17330,17395,17454,17513,17573,17633,17693,17753,17813,17873,17933,17993,18053,18113,18172,18232,18292,18352,18412,18472,18532,18592,18652,18712,18772,18831,18891,18951,19010,19069,19128,19187,19246,19305,19849,19884,20490,20553,20608,20666,20724,20785,20848,20905,20956,21006,21067,21124,21190,21224,21259,21294,22370,24552,24624,24693,24762,24836,24908,24996,28827,127902,128103,128279,128663,128858,140873,140940,162660,163362,172627,173308,174309,174476"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1d0a3f573332b3be097db894daafbd8d\\transformed\\media-1.1.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,144,215,288,341,394,447,500,560,626,748,809,875", "endColumns": "88,70,72,52,52,52,52,59,65,121,60,65,66", "endOffsets": "139,210,283,336,389,442,495,555,621,743,804,870,937"}, "to": {"startLines": "123,129,135,269,270,271,272,368,1975,1977,1978,1983,1985", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6434,6892,7295,16522,16575,16628,16681,21994,128108,128284,128406,128668,128863", "endColumns": "88,70,72,52,52,52,52,59,65,121,60,65,66", "endOffsets": "6518,6958,7363,16570,16623,16676,16729,22049,128169,128401,128462,128729,128925"}}, {"source": "D:\\20223\\2025\\legl92025\\android\\app\\build\\generated\\res\\processReleaseGoogleServices\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,199,281,385,494,614,715", "endColumns": "143,81,103,108,119,100,69", "endOffsets": "194,276,380,489,609,710,780"}, "to": {"startLines": "431,437,438,439,440,441,446", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "27440,27938,28020,28124,28233,28353,28638", "endColumns": "143,81,103,108,119,100,69", "endOffsets": "27579,28015,28119,28228,28348,28449,28703"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e579fafb1a43350ef4cd8b8959df0488\\transformed\\jetified-firebase-messaging-24.1.2\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "81", "endOffsets": "132"}, "to": {"startLines": "436", "startColumns": "4", "startOffsets": "27856", "endColumns": "81", "endOffsets": "27933"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\44a6db5ec6cee4d10c55825c1ba848aa\\transformed\\jetified-window-1.2.0\\res\\values\\values.xml", "from": {"startLines": "2,3,9,17,25,37,43,49,50,51,52,53,54,55,61,66,74,89", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,114,287,506,725,1039,1227,1414,1467,1527,1579,1624,1663,1723,1918,2076,2358,2972", "endLines": "2,8,16,24,36,42,48,49,50,51,52,53,54,60,65,73,88,104", "endColumns": "58,11,11,11,11,11,11,52,59,51,44,38,59,24,24,24,24,24", "endOffsets": "109,282,501,720,1034,1222,1409,1462,1522,1574,1619,1658,1718,1913,2071,2353,2967,3621"}, "to": {"startLines": "2,5,11,19,30,42,48,54,55,56,57,58,320,2249,2255,3579,3587,3602", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,280,453,672,1045,1359,1547,1734,1787,1847,1899,1944,19515,144613,144808,190293,190575,191189", "endLines": "2,10,18,26,41,47,53,54,55,56,57,58,320,2254,2259,3586,3601,3617", "endColumns": "58,11,11,11,11,11,11,52,59,51,44,38,59,24,24,24,24,24", "endOffsets": "159,448,667,886,1354,1542,1729,1782,1842,1894,1939,1978,19570,144803,144961,190570,191184,191838"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\76de88675158c32d011d90413b6fc543\\transformed\\preference-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,22,23,24,25,42,45,51,57,60,66,70,73,80,86,89,95,100,105,112,114,120,126,134,139,146,151,157,161,168,172,178,184,187,192,193,194,199,215,238,243,257,268,348,358,368,386,392,439,461,485", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,178,247,311,366,434,501,566,623,680,728,776,837,900,963,1001,1058,1102,1242,1381,1431,1479,2917,3022,3378,3716,3862,4202,4414,4577,4984,5322,5445,5784,6023,6280,6651,6711,7049,7335,7784,8076,8464,8769,9113,9358,9688,9895,10163,10436,10580,10949,10996,11052,11308,12367,13788,14126,15012,15622,20168,20687,21229,22503,22763,25467,26989,28470", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,21,22,23,24,41,44,50,56,59,65,69,72,79,85,88,94,99,104,111,113,119,125,133,138,145,150,156,160,167,171,177,183,186,191,192,193,198,214,237,242,256,267,347,357,367,385,391,438,460,484,508", "endColumns": "72,68,63,54,67,66,64,56,56,47,47,60,62,62,37,56,43,13,138,49,47,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,46,55,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "173,242,306,361,429,496,561,618,675,723,771,832,895,958,996,1053,1097,1237,1376,1426,1474,2912,3017,3373,3711,3857,4197,4409,4572,4979,5317,5440,5779,6018,6275,6646,6706,7044,7330,7779,8071,8459,8764,9108,9353,9683,9890,10158,10431,10575,10944,10991,11047,11303,12362,13783,14121,15007,15617,20163,20682,21224,22498,22758,25462,26984,28465,29984"}, "to": {"startLines": "63,124,262,263,264,265,266,267,268,329,330,331,371,372,429,432,442,443,449,450,451,1524,1708,1711,1717,1723,1726,1732,1736,1739,1746,1752,1755,1761,1766,1771,1778,1780,1786,1792,1800,1805,1812,1817,1823,1827,1834,1838,1844,1850,1853,1857,1858,2788,2803,2942,2980,3122,3310,3328,3392,3402,3412,3419,3425,3535,3704,3721", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2200,6523,16089,16153,16208,16276,16343,16408,16465,19932,19980,20028,22179,22242,27334,27584,28454,28498,28832,28971,29021,97227,110965,111070,111315,111653,111799,112139,112351,112514,112921,113259,113382,113721,113960,114217,114588,114648,114986,115272,115721,116013,116401,116706,117050,117295,117625,117832,118100,118373,118517,118718,118765,162142,162665,169451,170752,175694,181604,182232,184157,184439,184744,185006,185266,189112,195407,195937", "endLines": "63,124,262,263,264,265,266,267,268,329,330,331,371,372,429,432,442,445,449,450,451,1540,1710,1716,1722,1725,1731,1735,1738,1745,1751,1754,1760,1765,1770,1777,1779,1785,1791,1799,1804,1811,1816,1822,1826,1833,1837,1843,1849,1852,1856,1857,1858,2792,2813,2961,2983,3131,3317,3391,3401,3411,3418,3424,3467,3547,3720,3737", "endColumns": "72,68,63,54,67,66,64,56,56,47,47,60,62,62,37,56,43,13,138,49,47,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,46,55,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "2268,6587,16148,16203,16271,16338,16403,16460,16517,19975,20023,20084,22237,22300,27367,27636,28493,28633,28966,29016,29064,98660,111065,111310,111648,111794,112134,112346,112509,112916,113254,113377,113716,113955,114212,114583,114643,114981,115267,115716,116008,116396,116701,117045,117290,117620,117827,118095,118368,118512,118713,118760,118816,162322,163061,170175,170896,176021,181847,184152,184434,184739,185001,185261,186684,189559,195932,196500"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\aaa214e283e9fc31b460d74f33a3048c\\transformed\\fragment-1.7.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "321,337,365,3038,3043", "startColumns": "4,4,4,4,4", "startOffsets": "19575,20329,21804,173313,173483", "endLines": "321,337,365,3042,3046", "endColumns": "56,64,63,24,24", "endOffsets": "19627,20389,21863,173478,173627"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\09af1acd0243272a2fc2b8636ba12c20\\transformed\\jetified-activity-1.9.3\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "334,362", "startColumns": "4,4", "startOffsets": "20185,21640", "endColumns": "41,59", "endOffsets": "20222,21695"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\26d86c4b0b9a6035670ca0318f4a106b\\transformed\\jetified-android-pdf-viewer-3.2.0-beta.3\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "7", "endColumns": "24", "endOffsets": "380"}, "to": {"startLines": "3494", "startColumns": "4", "startOffsets": "187699", "endLines": "3499", "endColumns": "24", "endOffsets": "188024"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2904d3680b807a7b9a7de5b3adcbd1a4\\transformed\\lifecycle-runtime-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "361", "startColumns": "4", "startOffsets": "21597", "endColumns": "42", "endOffsets": "21635"}}]}]}