{"logs": [{"outputFile": "com.legal2025.yamy.app-mergeDebugResources-73:/values-es-rUS/values-es-rUS.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1214412e3b6f657b0a3026c46d581630\\transformed\\jetified-credentials-1.2.0-rc01\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,169", "endColumns": "113,121", "endOffsets": "164,286"}, "to": {"startLines": "29,30", "startColumns": "4,4", "startOffsets": "2821,2935", "endColumns": "113,121", "endOffsets": "2930,3052"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\13f2593a09a7e676e6a4e2c9d70ef7ac\\transformed\\core-1.13.1\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,356,454,561,667,787", "endColumns": "98,101,99,97,106,105,119,100", "endOffsets": "149,251,351,449,556,662,782,883"}, "to": {"startLines": "31,32,33,34,35,36,37,65", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3057,3156,3258,3358,3456,3563,3669,7019", "endColumns": "98,101,99,97,106,105,119,100", "endOffsets": "3151,3253,3353,3451,3558,3664,3784,7115"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e53585da63edf0e2fa4a6374876313c7\\transformed\\appcompat-1.1.0\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,334,442,527,629,745,830,911,1002,1094,1189,1283,1383,1476,1575,1671,1762,1853,1934,2041,2140,2239,2347,2455,2562,2721,2821", "endColumns": "119,108,107,84,101,115,84,80,90,91,94,93,99,92,98,95,90,90,80,106,98,98,107,107,106,158,99,81", "endOffsets": "220,329,437,522,624,740,825,906,997,1089,1184,1278,1378,1471,1570,1666,1757,1848,1929,2036,2135,2234,2342,2450,2557,2716,2816,2898"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,334,442,527,629,745,830,911,1002,1094,1189,1283,1383,1476,1575,1671,1762,1853,1934,2041,2140,2239,2347,2455,2562,2721,6937", "endColumns": "119,108,107,84,101,115,84,80,90,91,94,93,99,92,98,95,90,90,80,106,98,98,107,107,106,158,99,81", "endOffsets": "220,329,437,522,624,740,825,906,997,1089,1184,1278,1378,1471,1570,1666,1757,1848,1929,2036,2135,2234,2342,2450,2557,2716,2816,7014"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\305e40512bf13f0ccd5d12e0c8791b8d\\transformed\\jetified-play-services-base-18.1.0\\res\\values-es-rUS\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "197,301,463,591,695,852,979,1098,1200,1367,1472,1638,1767,1940,2114,2180,2238", "endColumns": "103,161,127,103,156,126,118,101,166,104,165,128,172,173,65,57,73", "endOffsets": "300,462,590,694,851,978,1097,1199,1366,1471,1637,1766,1939,2113,2179,2237,2311"}, "to": {"startLines": "38,39,40,41,42,43,44,45,47,48,49,50,51,52,53,54,55", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3789,3897,4063,4195,4303,4464,4595,4718,4970,5141,5250,5420,5553,5730,5908,5978,6040", "endColumns": "107,165,131,107,160,130,122,105,170,108,169,132,176,177,69,61,77", "endOffsets": "3892,4058,4190,4298,4459,4590,4713,4819,5136,5245,5415,5548,5725,5903,5973,6035,6113"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\76de88675158c32d011d90413b6fc543\\transformed\\preference-1.2.1\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,272,350,492,661,749", "endColumns": "69,96,77,141,168,87,81", "endOffsets": "170,267,345,487,656,744,826"}, "to": {"startLines": "56,58,62,63,66,67,68", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "6118,6297,6717,6795,7120,7289,7377", "endColumns": "69,96,77,141,168,87,81", "endOffsets": "6183,6389,6790,6932,7284,7372,7454"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5813dff2fc04f128ab917c6cfbab969f\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-es-rUS\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "199", "endColumns": "141", "endOffsets": "340"}, "to": {"startLines": "46", "startColumns": "4", "startOffsets": "4824", "endColumns": "145", "endOffsets": "4965"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7be1c0bb4075e2006ba23d0329c62fdb\\transformed\\browser-1.8.0\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,164,266,381", "endColumns": "108,101,114,105", "endOffsets": "159,261,376,482"}, "to": {"startLines": "57,59,60,61", "startColumns": "4,4,4,4", "startOffsets": "6188,6394,6496,6611", "endColumns": "108,101,114,105", "endOffsets": "6292,6491,6606,6712"}}]}]}