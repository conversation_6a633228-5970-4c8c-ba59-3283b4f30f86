import 'package:flutter/material.dart';
import 'package:syncfusion_flutter_pdfviewer/pdfviewer.dart';
import 'package:flutter/foundation.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:path_provider/path_provider.dart';
import 'dart:io';
import 'package:url_launcher/url_launcher.dart';

import '../models/subject.dart';
import '../models/pdf_model.dart';
import '../theme/app_theme.dart';
import '../widgets/progressive_pdf_viewer.dart';
import '../services/enhanced_pdf_service.dart';

// import '../utils/pdf_url_tester.dart'; // تم حذف الملف لتقليل الحجم

class PDFViewerScreen extends StatefulWidget {
  final Subject subject;
  final String? pdfFileName;
  final String? category;
  final String? pdfUrl; // رابط PDF الحقيقي
  final PDFModel? pdfModel; // نموذج PDF الكامل

  const PDFViewerScreen({
    super.key,
    required this.subject,
    this.pdfFileName,
    this.category,
    this.pdfUrl,
    this.pdfModel,
  });

  @override
  State<PDFViewerScreen> createState() => _PDFViewerScreenState();
}

class _PDFViewerScreenState extends State<PDFViewerScreen>
    with TickerProviderStateMixin {
  PdfViewerController? _pdfViewerController;
  late AnimationController _fabAnimationController;
  late Animation<double> _fabAnimation;

  bool _isLoading = true;
  bool _showControls = true;
  bool _hasError = false;
  int _currentPage = 1;
  int _totalPages = 0;
  String? _localPdfPath;
  String? _onlineUrl;
  double _downloadProgress = 0.0;

  @override
  void initState() {
    super.initState();
    _pdfViewerController = PdfViewerController();
    _initializePdfViewer();

    _fabAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _fabAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fabAnimationController, curve: Curves.easeInOut),
    );

    _fabAnimationController.forward();
  }

  void _initializePdfViewer() async {
    final String? pdfUrl = widget.pdfUrl ?? widget.pdfModel?.url;

    if (kDebugMode) {
      print('🔍 تهيئة عارض PDF:');
      print('📄 widget.pdfUrl: ${widget.pdfUrl}');
      print('📄 widget.pdfModel?.url: ${widget.pdfModel?.url}');
      print('📄 Final URL: $pdfUrl');
      print('📄 isFromUrl: ${widget.pdfModel?.isFromUrl}');
    }

    if (pdfUrl != null && pdfUrl.isNotEmpty) {
      // التحقق من نوع الملف
      if (widget.pdfModel?.isFromUrl == false || _isLocalFile(pdfUrl)) {
        // ملف محلي - تحميل مباشر
        await _loadLocalPdf(pdfUrl);
      } else {
        // ملف من URL - عرض أونلاين مباشر بدون أي تحميل
        await _setupOnlineViewing(pdfUrl);
      }
    } else {
      if (kDebugMode) {
        print('❌ لا يوجد رابط PDF صالح');
      }
      setState(() {
        _isLoading = false;
        _hasError = true;
      });
    }
  }

  /// التحقق من كون الملف محلي
  bool _isLocalFile(String path) {
    return path.startsWith('/') ||
        path.startsWith('file://') ||
        (path.contains(':') && !path.startsWith('http'));
  }

  /// تحميل ملف PDF محلي
  Future<void> _loadLocalPdf(String localPath) async {
    try {
      setState(() {
        _isLoading = true;
        _hasError = false;
      });

      if (kDebugMode) {
        print('📁 تحميل ملف PDF محلي: $localPath');
      }

      // التحقق من وجود الملف
      final file = File(localPath);
      if (await file.exists()) {
        setState(() {
          _localPdfPath = localPath;
          _isLoading = false;
          _downloadProgress = 1.0;
        });

        if (kDebugMode) {
          print('✅ تم تحميل الملف المحلي بنجاح');
        }
      } else {
        throw Exception('الملف غير موجود: $localPath');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في تحميل الملف المحلي: $e');
      }
      setState(() {
        _isLoading = false;
        _hasError = true;
      });
    }
  }

  /// إعداد العرض الأونلاين المباشر بدون تحميل
  Future<void> _setupOnlineViewing(String url) async {
    try {
      setState(() {
        _isLoading = true;
        _hasError = false;
      });

      if (kDebugMode) {
        print('🌐 إعداد عرض PDF أونلاين: $url');
      }

      // تحويل رابط Google Drive للعرض المباشر
      final String viewUrl = _convertToViewUrl(url);

      // إعداد العرض الأونلاين مباشرة
      setState(() {
        _onlineUrl = viewUrl;
        _isLoading = false;
        _downloadProgress = 1.0;
      });

      if (kDebugMode) {
        print('✅ تم إعداد عرض PDF أونلاين: $viewUrl');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في إعداد عرض PDF أونلاين: $e');
      }
      setState(() {
        _isLoading = false;
        _hasError = true;
      });
    }
  }

  /// الحصول على ملف مخزن مؤقتاً
  Future<File?> _getCachedFile(String url) async {
    try {
      final directory = await getTemporaryDirectory();
      final fileName = '${url.hashCode}.pdf';
      final file = File('${directory.path}/$fileName');
      return file;
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في الحصول على الملف المؤقت: $e');
      }
      return null;
    }
  }

  /// تحميل ملف PDF
  Future<void> _downloadPDF() async {
    final String? pdfUrl = widget.pdfUrl ?? widget.pdfModel?.url;

    if (pdfUrl == null || pdfUrl.isEmpty) {
      _showMessage('رابط الملف غير صالح', isError: true);
      return;
    }

    try {
      // عرض مؤشر التحميل
      if (mounted) {
        showDialog(
          context: context,
          barrierDismissible: false,
          builder:
              (context) => AlertDialog(
                content: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const CircularProgressIndicator(),
                    const SizedBox(height: 16),
                    Text('جاري تحميل الملف...', style: GoogleFonts.cairo()),
                  ],
                ),
              ),
        );
      }

      // استخدام الخدمة المحسنة للتحميل
      final fileName =
          widget.pdfFileName ??
          widget.pdfModel?.name ??
          'PDF_${DateTime.now().millisecondsSinceEpoch}';

      final filePath = await EnhancedPDFService.downloadPDFToLocal(
        url: pdfUrl,
        fileName: fileName,
      );

      // إغلاق مؤشر التحميل
      if (mounted) Navigator.of(context).pop();

      if (filePath != null) {
        _showMessage('تم تحميل الملف بنجاح');
        // تحديث العرض للملف المحلي
        setState(() {
          _localPdfPath = filePath;
          _onlineUrl = null;
        });
      } else {
        _showMessage('فشل في تحميل الملف', isError: true);
      }
    } catch (e) {
      // إغلاق مؤشر التحميل
      if (mounted) Navigator.of(context).pop();
      _showMessage('خطأ في تحميل الملف: $e', isError: true);
    }
  }

  /// عرض رسالة للمستخدم
  void _showMessage(String message, {bool isError = false}) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: isError ? Colors.red : Colors.green,
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }

  /// فتح PDF في المتصفح الخارجي
  Future<void> _openInBrowser(String url) async {
    try {
      final uri = Uri.parse(url);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      } else {
        _showMessage('لا يمكن فتح الرابط', isError: true);
      }
    } catch (e) {
      _showMessage('خطأ في فتح الرابط: $e', isError: true);
    }
  }

  /// تحميل فعلي للملف باستخدام EnhancedPDFService
  Future<void> _actualDownloadPdf(String url) async {
    try {
      if (kDebugMode) {
        print('🔄 بدء التحميل الفعلي: $url');
      }

      // استخدام الخدمة المحسنة للتحميل
      final fileName =
          widget.pdfFileName ??
          widget.pdfModel?.name ??
          'PDF_${DateTime.now().millisecondsSinceEpoch}';

      final filePath = await EnhancedPDFService.downloadPDFToLocal(
        url: url,
        fileName: fileName,
        onProgress: (progress) {
          if (kDebugMode) {
            print('📊 تقدم التحميل: ${(progress * 100).toStringAsFixed(1)}%');
          }
        },
      );

      if (filePath == null) {
        throw Exception('فشل في تحميل الملف');
      }

      if (kDebugMode) {
        print('✅ تم تحميل الملف بنجاح: $filePath');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في التحميل الفعلي: $e');
      }
      rethrow;
    }
  }

  /// تحويل روابط Google Drive إلى روابط عرض أو تحميل مباشرة
  String _convertToDirectDownloadUrl(String url, {bool forDownload = false}) {
    if (kDebugMode) {
      print('🔗 معالجة الرابط: $url (للتحميل: $forDownload)');
    }

    // إذا كان رابط Google Drive
    if (url.contains('drive.google.com')) {
      // نمط 1: /file/d/FILE_ID/view أو /file/d/FILE_ID/edit
      RegExp regex = RegExp(r'/file/d/([a-zA-Z0-9-_]+)');
      RegExpMatch? match = regex.firstMatch(url);

      if (match != null) {
        final fileId = match.group(1);
        final directUrl =
            forDownload
                ? 'https://drive.google.com/uc?export=download&id=$fileId&confirm=t'
                : 'https://drive.google.com/file/d/$fileId/preview';
        if (kDebugMode) {
          print('🔄 تحويل Google Drive: $fileId -> $directUrl');
        }
        return directUrl;
      }

      // نمط 2: إذا كان يحتوي على id= بالفعل
      regex = RegExp(r'[?&]id=([a-zA-Z0-9-_]+)');
      match = regex.firstMatch(url);
      if (match != null) {
        final fileId = match.group(1);
        final directUrl =
            forDownload
                ? 'https://drive.google.com/uc?export=download&id=$fileId&confirm=t'
                : 'https://drive.google.com/file/d/$fileId/preview';
        if (kDebugMode) {
          print('🔄 تحويل Google Drive (ID): $fileId -> $directUrl');
        }
        return directUrl;
      }

      // نمط 3: رابط مشاركة مع usp=sharing
      regex = RegExp(r'drive\.google\.com/file/d/([a-zA-Z0-9-_]+)/.*');
      match = regex.firstMatch(url);
      if (match != null) {
        final fileId = match.group(1);
        final directUrl =
            forDownload
                ? 'https://drive.google.com/uc?export=download&id=$fileId&confirm=t'
                : 'https://drive.google.com/file/d/$fileId/preview';
        if (kDebugMode) {
          print('🔄 تحويل Google Drive (مشاركة): $fileId -> $directUrl');
        }
        return directUrl;
      }
    }

    // إذا كان رابط Dropbox
    if (url.contains('dropbox.com') && url.contains('dl=0')) {
      final directUrl = url.replaceAll('dl=0', 'dl=1');
      if (kDebugMode) {
        print('🔄 تحويل Dropbox: $directUrl');
      }
      return directUrl;
    }

    // إذا كان رابط OneDrive
    if (url.contains('1drv.ms') || url.contains('onedrive.live.com')) {
      if (url.contains('?')) {
        final directUrl = '$url&download=1';
        if (kDebugMode) {
          print('🔄 تحويل OneDrive: $directUrl');
        }
        return directUrl;
      }
    }

    // إذا كان رابط مباشر، نعيده كما هو
    if (kDebugMode) {
      print('✅ رابط مباشر: $url');
    }
    return url;
  }

  /// تحويل رابط إلى رابط عرض مباشر (للعرض الأونلاين)
  String _convertToViewUrl(String url) {
    if (kDebugMode) {
      print('🔗 تحويل رابط للعرض: $url');
    }

    // إذا كان رابط Google Drive
    if (url.contains('drive.google.com')) {
      final fileId = _extractGoogleDriveFileId(url);
      if (fileId != null) {
        // استخدام رابط العرض المباشر
        final viewUrl = 'https://drive.google.com/file/d/$fileId/preview';
        if (kDebugMode) {
          print('🔄 تحويل Google Drive للعرض: $fileId -> $viewUrl');
        }
        return viewUrl;
      }
    }

    // للروابط الأخرى، إرجاع الرابط كما هو
    return url;
  }

  /// استخراج معرف الملف من رابط Google Drive
  String? _extractGoogleDriveFileId(String url) {
    // نمط 1: /file/d/FILE_ID/view أو /file/d/FILE_ID/edit
    RegExp regex = RegExp(r'/file/d/([a-zA-Z0-9-_]+)');
    RegExpMatch? match = regex.firstMatch(url);
    if (match != null) {
      return match.group(1);
    }

    // نمط 2: إذا كان يحتوي على id= بالفعل
    regex = RegExp(r'[?&]id=([a-zA-Z0-9-_]+)');
    match = regex.firstMatch(url);
    if (match != null) {
      return match.group(1);
    }

    // نمط 3: رابط مشاركة مع usp=sharing
    regex = RegExp(r'drive\.google\.com/file/d/([a-zA-Z0-9-_]+)/.*');
    match = regex.firstMatch(url);
    if (match != null) {
      return match.group(1);
    }

    return null;
  }

  @override
  void dispose() {
    _fabAnimationController.dispose();
    // تنظيف الملف المؤقت
    _cleanupTempFile();
    super.dispose();
  }

  /// تنظيف الملف المؤقت
  void _cleanupTempFile() async {
    if (_localPdfPath != null) {
      try {
        final file = File(_localPdfPath!);
        if (await file.exists()) {
          await file.delete();
          if (kDebugMode) {
            print('🗑️ تم حذف الملف المؤقت: $_localPdfPath');
          }
        }
      } catch (e) {
        if (kDebugMode) {
          print('⚠️ خطأ في حذف الملف المؤقت: $e');
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // استخدام العارض المحسن الجديد مع العرض الفوري
    final pdfUrl = widget.pdfUrl ?? widget.pdfModel?.url;
    final fileName = widget.pdfFileName ?? widget.pdfModel?.name ?? 'PDF';

    if (pdfUrl != null && pdfUrl.isNotEmpty) {
      return ProgressivePDFViewer(
        pdfUrl: pdfUrl,
        fileName: fileName,
        title: fileName,
      );
    }

    // في حالة عدم وجود رابط صالح - العودة للعارض القديم
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      body: SafeArea(
        child: Column(
          children: [
            // الهيدر
            _buildHeader(),

            // عارض PDF
            Expanded(
              child: Stack(
                children: [
                  // PDF Viewer
                  Container(
                    margin: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: AppTheme.cardShadow,
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(16),
                      child: _buildPdfViewer(),
                    ),
                  ),

                  // مؤشر التحميل
                  if (_isLoading)
                    Container(
                      margin: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: const Center(child: CircularProgressIndicator()),
                    ),

                  // أدوات التحكم العائمة
                  if (_showControls && !_isLoading) _buildFloatingControls(),
                ],
              ),
            ),

            // شريط المعلومات السفلي
            if (!_isLoading) _buildBottomInfo(),
          ],
        ),
      ),
    );
  }

  /// بناء عارض PDF المناسب
  Widget _buildPdfViewer() {
    if (kDebugMode) {
      print('🔍 PDF Viewer Debug:');
      print('📄 localPdfPath: $_localPdfPath');
      print('📄 onlineUrl: $_onlineUrl');
      print('📄 isLoading: $_isLoading');
      print('📄 hasError: $_hasError');
    }

    // إذا كان هناك خطأ
    if (_hasError) {
      return _buildErrorView('فشل في تحميل الملف');
    }

    // إذا كان هناك ملف محلي
    if (_localPdfPath != null) {
      return _buildSimplePdfViewer(_localPdfPath!, isLocal: true);
    }

    // إذا كان هناك رابط للعرض المباشر
    if (_onlineUrl != null) {
      return _buildSimplePdfViewer(_onlineUrl!, isLocal: false);
    }

    // إذا لم يتم تحميل الملف بعد
    return _buildLoadingView();
  }

  /// عرض شاشة التحميل مع شريط التقدم
  Widget _buildLoadingView() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(Icons.download_rounded, size: 80, color: AppTheme.primaryColor),
        const SizedBox(height: 24),
        Text(
          'جاري تحميل الملف...',
          style: GoogleFonts.cairo(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.grey[800],
          ),
        ),
        const SizedBox(height: 16),
        SizedBox(
          width: 200,
          child: LinearProgressIndicator(
            value: _downloadProgress,
            backgroundColor: Colors.grey[300],
            valueColor: AlwaysStoppedAnimation<Color>(AppTheme.primaryColor),
          ),
        ),
        const SizedBox(height: 8),
        Text(
          '${(_downloadProgress * 100).toInt()}%',
          style: GoogleFonts.cairo(fontSize: 14, color: Colors.grey[600]),
        ),
      ],
    );
  }

  /// عارض PDF مبسط وموحد
  Widget _buildSimplePdfViewer(String source, {required bool isLocal}) {
    if (kDebugMode) {
      print('📄 عرض PDF ${isLocal ? 'محلي' : 'أونلاين'}: $source');
    }

    try {
      // للملفات المحلية
      if (isLocal) {
        final file = File(source);
        if (!file.existsSync()) {
          return _buildErrorView('الملف غير موجود');
        }

        return SfPdfViewer.file(
          file,
          controller: _pdfViewerController,
          // تحسينات الأداء للملفات المحلية
          enableDoubleTapZooming: true,
          enableTextSelection: false, // تعطيل لتوفير 40% من الذاكرة
          canShowScrollHead: true, // تفعيل للتنقل السريع
          canShowScrollStatus: true, // عرض حالة التمرير
          canShowPaginationDialog: false, // تعطيل لتحسين الأداء
          pageLayoutMode:
              PdfPageLayoutMode.single, // عرض صفحة واحدة لتوفير الذاكرة
          scrollDirection: PdfScrollDirection.vertical,
          pageSpacing: 2, // تقليل المساحة لتحسين الأداء
          onDocumentLoaded: (PdfDocumentLoadedDetails details) {
            setState(() {
              _totalPages = details.document.pages.count;
              _isLoading = false;
            });
            if (kDebugMode) {
              print(
                '✅ تم تحميل PDF محلي محسن: ${details.document.pages.count} صفحة',
              );
              print(
                '🚀 تحسينات الأداء مفعلة للملفات الكبيرة: ${details.document.pages.count > 50}',
              );
            }
          },
          onDocumentLoadFailed: (PdfDocumentLoadFailedDetails details) {
            setState(() {
              _isLoading = false;
              _hasError = true;
            });
            if (kDebugMode) {
              print('❌ خطأ في تحميل PDF محلي: ${details.error}');
            }
          },
          onPageChanged: (PdfPageChangedDetails details) {
            setState(() {
              _currentPage = details.newPageNumber;
            });
          },
        );
      }
      // للملفات الأونلاين - عرض مباشر بدون تحميل
      else {
        final directUrl = _convertToViewUrl(source);

        return SfPdfViewer.network(
          directUrl,
          controller: _pdfViewerController,
          // تحسينات الأداء للملفات الأونلاين
          enableDoubleTapZooming: true,
          enableTextSelection: false, // تعطيل لتوفير الذاكرة وتسريع التحميل
          canShowScrollHead: true, // تفعيل للتنقل السريع
          canShowScrollStatus: true, // عرض حالة التمرير
          canShowPaginationDialog: false, // تعطيل لتحسين الأداء
          pageLayoutMode:
              PdfPageLayoutMode.single, // عرض صفحة واحدة لتحميل أسرع
          scrollDirection: PdfScrollDirection.vertical,
          pageSpacing: 2, // تقليل المساحة لتحسين الأداء
          onDocumentLoaded: (PdfDocumentLoadedDetails details) {
            setState(() {
              _totalPages = details.document.pages.count;
              _isLoading = false;
            });
            if (kDebugMode) {
              print(
                '✅ تم تحميل PDF أونلاين محسن: ${details.document.pages.count} صفحة',
              );
              print(
                '🚀 تحسينات الأداء مفعلة للملفات الكبيرة: ${details.document.pages.count > 50}',
              );
              print('📊 توفير الذاكرة: تم تعطيل التحديد النصي');
            }
          },
          onDocumentLoadFailed: (PdfDocumentLoadFailedDetails details) {
            setState(() {
              _isLoading = false;
              _hasError = true;
            });
            if (kDebugMode) {
              print('❌ خطأ في تحميل PDF أونلاين: ${details.error}');
              print('🔗 الرابط المستخدم: $directUrl');
            }
          },
          onPageChanged: (PdfPageChangedDetails details) {
            setState(() {
              _currentPage = details.newPageNumber;
            });
          },
          headers: {
            'User-Agent': 'Mozilla/5.0 (Linux; Android 10) AppleWebKit/537.36',
            'Accept': 'application/pdf,*/*',
          },
        );
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في إنشاء عارض PDF: $e');
      }
      return _buildErrorView('خطأ في عرض PDF: $e');
    }
  }

  /// عرض شاشة الخطأ
  Widget _buildErrorView([String? message]) {
    final String? pdfUrl = widget.pdfUrl ?? widget.pdfModel?.url;
    if (pdfUrl == null || pdfUrl.isEmpty) {
      return _buildErrorView('رابط PDF غير صالح');
    }

    // تحويل إلى رابط مباشر
    final String directUrl = _convertToDirectDownloadUrl(pdfUrl);

    if (kIsWeb) {
      return Container(
        width: double.infinity,
        height: double.infinity,
        decoration: BoxDecoration(
          color: Colors.grey.shade100,
          borderRadius: BorderRadius.circular(8),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: HtmlElementView(
            viewType: 'pdf-viewer-${DateTime.now().millisecondsSinceEpoch}',
            onPlatformViewCreated: (id) {
              // تم إزالة هذا للأندرويد
            },
          ),
        ),
      );
    }

    // للأندرويد - عرض خيارات للمستخدم
    if (Platform.isAndroid || Platform.isIOS) {
      return Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.picture_as_pdf_rounded,
              size: 100,
              color: Colors.red.shade400,
            ),
            const SizedBox(height: 24),
            Text(
              'عرض ملف PDF',
              style: GoogleFonts.cairo(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.grey[800],
              ),
            ),
            const SizedBox(height: 16),
            Text(
              'اختر طريقة عرض الملف:',
              style: GoogleFonts.cairo(fontSize: 16, color: Colors.grey[600]),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),

            // زر فتح في المتصفح
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () => _openInBrowser(directUrl),
                icon: const Icon(Icons.open_in_browser),
                label: Text(
                  'فتح في المتصفح',
                  style: GoogleFonts.cairo(fontSize: 16),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF10B981),
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ),
            ),

            const SizedBox(height: 16),

            // زر التحميل
            SizedBox(
              width: double.infinity,
              child: OutlinedButton.icon(
                onPressed: _downloadPDF,
                icon: const Icon(Icons.download_rounded),
                label: Text(
                  'تحميل الملف',
                  style: GoogleFonts.cairo(fontSize: 16),
                ),
                style: OutlinedButton.styleFrom(
                  foregroundColor: const Color(0xFF10B981),
                  side: const BorderSide(color: Color(0xFF10B981)),
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ),
            ),
          ],
        ),
      );
    }

    // للمنصات الأخرى - عرض بديل
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(
          Icons.picture_as_pdf_rounded,
          size: 80,
          color: Colors.red.shade400,
        ),
        const SizedBox(height: 24),
        Text(
          'عارض PDF - الويب',
          style: GoogleFonts.cairo(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: Colors.grey[800],
          ),
        ),
        const SizedBox(height: 16),
        Container(
          padding: const EdgeInsets.all(16),
          margin: const EdgeInsets.symmetric(horizontal: 20),
          decoration: BoxDecoration(
            color: Colors.blue.shade50,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.blue.shade200),
          ),
          child: Column(
            children: [
              Text(
                widget.pdfModel?.name ?? 'ملف PDF',
                style: GoogleFonts.cairo(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.blue.shade800,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 12),
              Text(
                'عارض PDF لا يعمل على الويب.\nاختر طريقة العرض المناسبة:',
                style: GoogleFonts.cairo(fontSize: 14, color: Colors.grey[600]),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
        const SizedBox(height: 24),

        // خيارات متعددة للعرض
        Wrap(
          spacing: 16,
          runSpacing: 16,
          alignment: WrapAlignment.center,
          children: [
            // فتح في نافذة جديدة
            ElevatedButton.icon(
              onPressed: () => _openInNewTab(directUrl),
              icon: const Icon(Icons.open_in_new, color: Colors.white),
              label: Text(
                'فتح في نافذة جديدة',
                style: GoogleFonts.cairo(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                ),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue.shade600,
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                elevation: 4,
              ),
            ),

            // تحميل الملف
            OutlinedButton.icon(
              onPressed: () => _downloadFile(directUrl),
              icon: Icon(Icons.download, color: Colors.green.shade600),
              label: Text(
                'تحميل الملف',
                style: GoogleFonts.cairo(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Colors.green.shade600,
                ),
              ),
              style: OutlinedButton.styleFrom(
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                side: BorderSide(color: Colors.green.shade600),
              ),
            ),

            // عرض الرابط المباشر
            TextButton.icon(
              onPressed: () => _showDirectUrl(directUrl),
              icon: Icon(Icons.link, color: Colors.orange.shade600),
              label: Text(
                'عرض الرابط المباشر',
                style: GoogleFonts.cairo(
                  fontSize: 14,
                  color: Colors.orange.shade600,
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// فتح الرابط في نافذة جديدة
  void _openInNewTab(String url) async {
    try {
      final Uri uri = Uri.parse(url);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      } else {
        if (kDebugMode) {
          print('❌ لا يمكن فتح الرابط: $url');
        }
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('لا يمكن فتح الرابط', style: GoogleFonts.cairo()),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في فتح الرابط: $e');
      }
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في فتح الرابط: $e', style: GoogleFonts.cairo()),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// إعادة تحميل PDF
  void _retryDownload() async {
    final String? pdfUrl = widget.pdfUrl ?? widget.pdfModel?.url;
    if (pdfUrl != null && pdfUrl.isNotEmpty) {
      // التحقق من نوع الملف
      if (widget.pdfModel?.isFromUrl == false || _isLocalFile(pdfUrl)) {
        // ملف محلي
        await _loadLocalPdf(pdfUrl);
      } else {
        // ملف من URL - عرض أونلاين مباشر
        await _setupOnlineViewing(pdfUrl);
      }
    }
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: const Icon(Icons.arrow_back_ios),
            style: IconButton.styleFrom(
              backgroundColor: AppTheme.backgroundColor,
              padding: const EdgeInsets.all(8),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.pdfFileName?.replaceAll('.pdf', '') ??
                      widget.subject.arabicName,
                  style: Theme.of(
                    context,
                  ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                Text(
                  widget.category != null
                      ? '${widget.category} • ${widget.subject.arabicName}'
                      : 'عارض PDF',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppTheme.textSecondaryColor,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
          // زر التحميل
          if (widget.pdfModel?.isFromUrl !=
              false) // إظهار زر التحميل فقط للملفات الأونلاين
            IconButton(
              onPressed: _downloadPDF,
              icon: const Icon(Icons.download_rounded),
              style: IconButton.styleFrom(
                backgroundColor: const Color(0xFF10B981),
                foregroundColor: Colors.white,
                padding: const EdgeInsets.all(8),
              ),
              tooltip: 'تحميل الملف',
            ),
          const SizedBox(width: 8),
          IconButton(
            onPressed: () {
              setState(() {
                _showControls = !_showControls;
              });
            },
            icon: Icon(_showControls ? Icons.visibility_off : Icons.visibility),
            style: IconButton.styleFrom(
              backgroundColor: AppTheme.backgroundColor,
              padding: const EdgeInsets.all(8),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFloatingControls() {
    return Positioned(
      top: 20,
      right: 20,
      child: FadeTransition(
        opacity: _fabAnimation,
        child: Column(
          children: [
            // إعادة تحميل
            FloatingActionButton.small(
              onPressed: _hasError ? _retryDownload : null,
              backgroundColor: Colors.white,
              foregroundColor: _hasError ? Colors.red : Colors.grey,
              child: const Icon(Icons.refresh),
            ),
            const SizedBox(height: 8),

            // الصفحة السابقة
            FloatingActionButton.small(
              onPressed:
                  _currentPage > 1 && _pdfViewerController != null
                      ? () async {
                        _pdfViewerController!.previousPage();
                      }
                      : null,
              backgroundColor: Colors.white,
              foregroundColor: AppTheme.primaryColor,
              child: const Icon(Icons.keyboard_arrow_up),
            ),
            const SizedBox(height: 8),

            // الصفحة التالية
            FloatingActionButton.small(
              onPressed:
                  _currentPage < _totalPages && _pdfViewerController != null
                      ? () async {
                        _pdfViewerController!.nextPage();
                      }
                      : null,
              backgroundColor: Colors.white,
              foregroundColor: AppTheme.primaryColor,
              child: const Icon(Icons.keyboard_arrow_down),
            ),
            const SizedBox(height: 8),

            // الانتقال إلى صفحة محددة
            FloatingActionButton.small(
              onPressed: _totalPages > 0 ? _showPageSelector : null,
              backgroundColor: Colors.white,
              foregroundColor: AppTheme.primaryColor,
              child: const Icon(Icons.format_list_numbered),
            ),
            const SizedBox(height: 8),

            // تحميل الملف (فقط للملفات من URL)
            if (_onlineUrl != null)
              FloatingActionButton.small(
                onPressed: _downloadPdfFile,
                backgroundColor: Colors.white,
                foregroundColor: Colors.green,
                child: const Icon(Icons.download),
              ),
          ],
        ),
      ),
    );
  }

  /// عرض منتقي الصفحات
  void _showPageSelector() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(
              'الانتقال إلى صفحة',
              style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'الصفحة الحالية: $_currentPage من $_totalPages',
                  style: GoogleFonts.cairo(),
                ),
                const SizedBox(height: 16),
                Slider(
                  value: _currentPage.toDouble(),
                  min: 1,
                  max: _totalPages.toDouble(),
                  divisions: _totalPages - 1,
                  label: _currentPage.toString(),
                  onChanged: (value) async {
                    final page = value.toInt();
                    final navigator = Navigator.of(context);
                    if (_pdfViewerController != null) {
                      _pdfViewerController!.jumpToPage(page);
                    }
                    if (mounted) {
                      navigator.pop();
                    }
                  },
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: Text('إلغاء', style: GoogleFonts.cairo()),
              ),
            ],
          ),
    );
  }

  Widget _buildBottomInfo() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            'الصفحة $_currentPage من $_totalPages',
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w500),
          ),
          Row(
            children: [
              Icon(
                Icons.picture_as_pdf,
                size: 16,
                color: AppTheme.textSecondaryColor,
              ),
              const SizedBox(width: 4),
              Text(
                'PDF محلي',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: AppTheme.textSecondaryColor,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// تحميل الملف
  void _downloadFile(String url) async {
    try {
      final Uri uri = Uri.parse(url);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('لا يمكن تحميل الملف', style: GoogleFonts.cairo()),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل الملف: $e', style: GoogleFonts.cairo()),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// تحميل ملف PDF (اختياري)
  Future<void> _downloadPdfFile() async {
    if (_onlineUrl == null) return;

    try {
      // إظهار مؤشر التحميل
      showDialog(
        context: context,
        barrierDismissible: false,
        builder:
            (context) => AlertDialog(
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const CircularProgressIndicator(),
                  const SizedBox(height: 16),
                  Text('جاري تحميل الملف...', style: GoogleFonts.cairo()),
                ],
              ),
            ),
      );

      // بدء التحميل الفعلي للملف
      await _actualDownloadPdf(_onlineUrl!);

      // إغلاق مؤشر التحميل
      if (mounted) {
        Navigator.of(context).pop();

        // إظهار رسالة نجاح
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم تحميل الملف بنجاح', style: GoogleFonts.cairo()),
            backgroundColor: Colors.green,
          ),
        );

        // تحديث العرض للملف المحلي
        final cachedFile = await _getCachedFile(_onlineUrl!);
        if (cachedFile != null && await cachedFile.exists()) {
          setState(() {
            _localPdfPath = cachedFile.path;
            _onlineUrl = null; // إزالة URL لأن الملف أصبح محلي
          });
        }
      }
    } catch (e) {
      // إغلاق مؤشر التحميل
      if (mounted) {
        Navigator.of(context).pop();

        // إظهار رسالة خطأ
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل في تحميل الملف: $e', style: GoogleFonts.cairo()),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// عرض الرابط المباشر
  void _showDirectUrl(String url) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(
              'الرابط المباشر',
              style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'يمكنك نسخ هذا الرابط واستخدامه مباشرة:',
                  style: GoogleFonts.cairo(),
                ),
                const SizedBox(height: 12),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.grey[100],
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.grey[300]!),
                  ),
                  child: SelectableText(
                    url,
                    style: GoogleFonts.cairo(fontSize: 12),
                  ),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: Text('إغلاق', style: GoogleFonts.cairo()),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  _openInNewTab(url);
                },
                child: Text('فتح الرابط', style: GoogleFonts.cairo()),
              ),
            ],
          ),
    );
  }
}
