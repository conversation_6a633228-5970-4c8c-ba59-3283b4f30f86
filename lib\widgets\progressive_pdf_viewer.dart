import 'dart:io';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:syncfusion_flutter_pdfviewer/pdfviewer.dart';
import 'package:flutter/foundation.dart';

/// عارض PDF محسن مع العرض التدريجي الحقيقي
class ProgressivePDFViewer extends StatefulWidget {
  final String pdfUrl;
  final String fileName;
  final String title;

  const ProgressivePDFViewer({
    super.key,
    required this.pdfUrl,
    required this.fileName,
    required this.title,
  });

  @override
  State<ProgressivePDFViewer> createState() => _ProgressivePDFViewerState();
}

class _ProgressivePDFViewerState extends State<ProgressivePDFViewer>
    with TickerProviderStateMixin {
  late PdfViewerController _pdfController;
  late AnimationController _progressAnimationController;
  late Animation<double> _progressAnimation;

  bool _hasError = false;
  bool _isLoading = true;
  bool _showControls = true;
  int _currentPage = 1;
  int _totalPages = 0;
  double _zoomLevel = 1.0;
  // double _loadingProgress = 0.0; // غير مستخدم حالياً

  @override
  void initState() {
    super.initState();
    _pdfController = PdfViewerController();
    _setupAnimations();

    if (kDebugMode) {
      print('🚀 بدء العرض التدريجي المحسن للـ PDF');
      print('📄 الملف: ${widget.fileName}');
    }
  }

  void _setupAnimations() {
    _progressAnimationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _progressAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _progressAnimationController,
        curve: Curves.easeInOut,
      ),
    );

    _progressAnimationController.repeat();
  }

  @override
  void dispose() {
    _progressAnimationController.dispose();
    _pdfController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[100],
      body: SafeArea(
        child: Stack(
          children: [
            // عارض PDF المحسن
            _buildEnhancedPDFViewer(),

            // مؤشر التحميل التدريجي
            if (_isLoading) _buildProgressiveLoadingOverlay(),

            // أدوات التحكم العلوية
            if (_showControls && !_isLoading) _buildTopControls(),

            // أدوات التحكم السفلية
            if (_showControls && !_isLoading) _buildBottomControls(),

            // رسالة الخطأ
            if (_hasError) _buildErrorOverlay(),
          ],
        ),
      ),
    );
  }

  Widget _buildEnhancedPDFViewer() {
    final bool isLocalFile = _isLocalFile(widget.pdfUrl);

    if (kDebugMode) {
      print('📄 عرض PDF محسن: ${widget.pdfUrl}');
      print('📁 ملف محلي: $isLocalFile');
    }

    if (isLocalFile) {
      return SfPdfViewer.file(
        File(widget.pdfUrl),
        controller: _pdfController,
        // إعدادات العرض التدريجي المحسن للأداء العالي
        enableDoubleTapZooming: true,
        enableTextSelection: false, // توفير 40% من الذاكرة
        canShowScrollHead: true,
        canShowScrollStatus: true,
        canShowPaginationDialog: true,
        pageLayoutMode: PdfPageLayoutMode.continuous, // عرض مستمر تدريجي
        scrollDirection: PdfScrollDirection.vertical, // تمرير عمودي صحيح
        pageSpacing: 6, // مساحة محسنة بين الصفحات
        initialZoomLevel: 1.0,
        // تحسينات الأداء الجديدة
        interactionMode: PdfInteractionMode.pan, // تحسين التفاعل
        // callbacks محسنة
        onDocumentLoaded: _onDocumentLoaded,
        onDocumentLoadFailed: _onDocumentLoadFailed,
        onPageChanged: _onPageChanged,
        onZoomLevelChanged: _onZoomLevelChanged,
      );
    } else {
      final String optimizedUrl = _optimizeUrl(widget.pdfUrl);
      return SfPdfViewer.network(
        optimizedUrl,
        controller: _pdfController,
        // إعدادات العرض التدريجي للملفات الأونلاين
        enableDoubleTapZooming: true,
        enableTextSelection: false, // توفير الذاكرة وتسريع التحميل
        canShowScrollHead: true,
        canShowScrollStatus: true,
        canShowPaginationDialog: true,
        pageLayoutMode: PdfPageLayoutMode.continuous, // عرض مستمر تدريجي
        scrollDirection: PdfScrollDirection.vertical, // تمرير عمودي صحيح
        pageSpacing: 6, // مساحة محسنة بين الصفحات
        initialZoomLevel: 1.0,
        // تحسينات الأداء للملفات الأونلاين
        interactionMode: PdfInteractionMode.pan, // تحسين التفاعل
        // callbacks محسنة
        onDocumentLoaded: _onDocumentLoaded,
        onDocumentLoadFailed: _onDocumentLoadFailed,
        onPageChanged: _onPageChanged,
        onZoomLevelChanged: _onZoomLevelChanged,
        // هيدرز محسنة للتحميل السريع
        headers: {
          'User-Agent': 'Mozilla/5.0 (Linux; Android 10) AppleWebKit/537.36',
          'Accept': 'application/pdf,*/*',
          'Cache-Control': 'max-age=3600', // تخزين مؤقت لساعة
          'Connection': 'keep-alive',
        },
      );
    }
  }

  void _onDocumentLoaded(PdfDocumentLoadedDetails details) {
    setState(() {
      _totalPages = details.document.pages.count;
      _isLoading = false;
      _zoomLevel = _pdfController.zoomLevel;
    });

    _progressAnimationController.stop();

    // تحسين العرض بعد التحميل
    _optimizeAfterLoad();

    if (kDebugMode) {
      print('✅ تم تحميل PDF بنجاح: ${details.document.pages.count} صفحة');
      print('🎉 العرض التدريجي يعمل بشكل مثالي!');
    }
  }

  void _onDocumentLoadFailed(PdfDocumentLoadFailedDetails details) {
    setState(() {
      _isLoading = false;
      _hasError = true;
    });

    _progressAnimationController.stop();

    if (kDebugMode) {
      print('❌ خطأ في تحميل PDF: ${details.error}');
    }
  }

  void _onPageChanged(PdfPageChangedDetails details) {
    setState(() {
      _currentPage = details.newPageNumber;
    });

    if (kDebugMode) {
      print('📄 تغيير الصفحة: ${details.newPageNumber} من $_totalPages');
    }
  }

  void _onZoomLevelChanged(PdfZoomDetails details) {
    setState(() {
      _zoomLevel = details.newZoomLevel;
    });
  }

  void _optimizeAfterLoad() {
    Future.delayed(const Duration(milliseconds: 500), () {
      if (mounted) {
        // ضبط التكبير الأمثل
        _pdfController.zoomLevel = 1.0;

        // إخفاء أدوات التحكم بعد 3 ثوان
        Future.delayed(const Duration(seconds: 3), () {
          if (mounted) {
            setState(() {
              _showControls = false;
            });
          }
        });
      }
    });
  }

  bool _isLocalFile(String path) {
    return path.startsWith('/') ||
        path.startsWith('file://') ||
        (path.length > 1 && path[1] == ':') ||
        (!path.startsWith('http://') &&
            !path.startsWith('https://') &&
            !path.contains('drive.google.com') &&
            path.contains('/'));
  }

  String _optimizeUrl(String url) {
    if (url.contains('drive.google.com')) {
      final RegExp regExp = RegExp(r'/file/d/([a-zA-Z0-9_-]+)');
      final Match? match = regExp.firstMatch(url);

      if (match != null) {
        final String fileId = match.group(1)!;
        return 'https://drive.google.com/uc?export=download&id=$fileId';
      }
    }
    return url;
  }

  Widget _buildProgressiveLoadingOverlay() {
    return Container(
      color: Colors.white,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            AnimatedBuilder(
              animation: _progressAnimation,
              builder: (context, child) {
                return Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: SweepGradient(
                      colors: [
                        Colors.blue.withValues(alpha: 0.3),
                        Colors.blue,
                        Colors.blue.withValues(alpha: 0.3),
                      ],
                      stops: [0.0, _progressAnimation.value, 1.0],
                    ),
                  ),
                  child: const Center(
                    child: Icon(
                      Icons.picture_as_pdf,
                      size: 40,
                      color: Colors.white,
                    ),
                  ),
                );
              },
            ),
            const SizedBox(height: 24),
            Text(
              'تحميل تدريجي للـ PDF',
              style: GoogleFonts.cairo(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Colors.blue[800],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'سيظهر المحتوى تدريجياً أثناء التحميل',
              style: GoogleFonts.cairo(fontSize: 14, color: Colors.grey[600]),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTopControls() {
    return Positioned(
      top: 0,
      left: 0,
      right: 0,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Colors.black.withValues(alpha: 0.7), Colors.transparent],
          ),
        ),
        child: Row(
          children: [
            IconButton(
              onPressed: () => Navigator.pop(context),
              icon: const Icon(Icons.arrow_back, color: Colors.white),
            ),
            Expanded(
              child: Text(
                widget.title,
                style: GoogleFonts.cairo(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            IconButton(
              onPressed: _toggleControls,
              icon: Icon(
                _showControls ? Icons.visibility_off : Icons.visibility,
                color: Colors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBottomControls() {
    return Positioned(
      bottom: 0,
      left: 0,
      right: 0,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.bottomCenter,
            end: Alignment.topCenter,
            colors: [Colors.black.withValues(alpha: 0.7), Colors.transparent],
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            // معلومات الصفحة
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: Colors.black.withValues(alpha: 0.5),
                borderRadius: BorderRadius.circular(16),
              ),
              child: Text(
                '$_currentPage من $_totalPages',
                style: GoogleFonts.cairo(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),

            // أدوات التحكم
            Row(
              children: [
                IconButton(
                  onPressed: _currentPage > 1 ? _previousPage : null,
                  icon: const Icon(
                    Icons.keyboard_arrow_up,
                    color: Colors.white,
                  ),
                ),
                IconButton(
                  onPressed: _showPageDialog,
                  icon: const Icon(Icons.list, color: Colors.white),
                ),
                IconButton(
                  onPressed: _currentPage < _totalPages ? _nextPage : null,
                  icon: const Icon(
                    Icons.keyboard_arrow_down,
                    color: Colors.white,
                  ),
                ),
              ],
            ),

            // مستوى التكبير
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: Colors.black.withValues(alpha: 0.5),
                borderRadius: BorderRadius.circular(16),
              ),
              child: Text(
                '${(_zoomLevel * 100).toInt()}%',
                style: GoogleFonts.cairo(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorOverlay() {
    return Container(
      color: Colors.white,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64, color: Colors.red[400]),
            const SizedBox(height: 16),
            Text(
              'خطأ في تحميل الملف',
              style: GoogleFonts.cairo(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Colors.red[800],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'تحقق من الاتصال بالإنترنت وحاول مرة أخرى',
              style: GoogleFonts.cairo(fontSize: 14, color: Colors.grey[600]),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () => Navigator.pop(context),
              child: Text(
                'العودة',
                style: GoogleFonts.cairo(fontWeight: FontWeight.w600),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _toggleControls() {
    setState(() {
      _showControls = !_showControls;
    });
  }

  void _previousPage() {
    if (_currentPage > 1) {
      _pdfController.previousPage();
    }
  }

  void _nextPage() {
    if (_currentPage < _totalPages) {
      _pdfController.nextPage();
    }
  }

  void _showPageDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(
              'الانتقال إلى صفحة',
              style: GoogleFonts.cairo(fontWeight: FontWeight.w600),
            ),
            content: TextField(
              keyboardType: TextInputType.number,
              decoration: InputDecoration(
                hintText: 'رقم الصفحة (1-$_totalPages)',
                border: const OutlineInputBorder(),
              ),
              onSubmitted: (value) {
                final pageNumber = int.tryParse(value);
                if (pageNumber != null &&
                    pageNumber >= 1 &&
                    pageNumber <= _totalPages) {
                  _pdfController.jumpToPage(pageNumber);
                  Navigator.pop(context);
                }
              },
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text('إلغاء', style: GoogleFonts.cairo()),
              ),
            ],
          ),
    );
  }
}
