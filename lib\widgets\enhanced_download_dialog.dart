import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import '../providers/theme_provider.dart';
import '../models/pdf_model.dart';

/// Dialog تحميل محسن مع واجهة جميلة ومعبرة
class EnhancedDownloadDialog extends StatefulWidget {
  final PDFModel pdf;
  final Function(double)? onProgress;
  final VoidCallback? onComplete;
  final Function(String)? onError;

  const EnhancedDownloadDialog({
    super.key,
    required this.pdf,
    this.onProgress,
    this.onComplete,
    this.onError,
  });

  @override
  State<EnhancedDownloadDialog> createState() => _EnhancedDownloadDialogState();
}

class _EnhancedDownloadDialogState extends State<EnhancedDownloadDialog>
    with TickerProviderStateMixin {
  late AnimationController _rotationController;
  late AnimationController _scaleController;
  late Animation<double> _rotationAnimation;
  late Animation<double> _scaleAnimation;

  double _progress = 0.0;
  bool _isCompleted = false;
  String _statusMessage = 'بدء التحميل...';

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _startDownload();
  }

  void _setupAnimations() {
    _rotationController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );

    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _rotationAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _rotationController,
      curve: Curves.linear,
    ));

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.elasticOut,
    ));

    _rotationController.repeat();
    _scaleController.forward();
  }

  void _startDownload() {
    // محاكاة التحميل مع تحديث التقدم
    _updateProgress(0.1, 'الاتصال بالخادم...');
    
    Future.delayed(const Duration(milliseconds: 500), () {
      _updateProgress(0.3, 'بدء تحميل الملف...');
    });
    
    Future.delayed(const Duration(milliseconds: 1000), () {
      _updateProgress(0.6, 'تحميل البيانات...');
    });
    
    Future.delayed(const Duration(milliseconds: 1500), () {
      _updateProgress(0.9, 'حفظ الملف...');
    });
    
    Future.delayed(const Duration(milliseconds: 2000), () {
      _completeDownload();
    });
  }

  void _updateProgress(double progress, String message) {
    if (mounted) {
      setState(() {
        _progress = progress;
        _statusMessage = message;
      });
      widget.onProgress?.call(progress);
    }
  }

  void _completeDownload() {
    if (mounted) {
      setState(() {
        _progress = 1.0;
        _isCompleted = true;
        _statusMessage = 'تم التحميل بنجاح!';
      });
      
      _rotationController.stop();
      _scaleController.forward();
      
      widget.onComplete?.call();
      
      // إغلاق تلقائي بعد ثانية
      Future.delayed(const Duration(milliseconds: 1000), () {
        if (mounted) {
          Navigator.of(context).pop();
        }
      });
    }
  }

  @override
  void dispose() {
    _rotationController.dispose();
    _scaleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return Dialog(
          backgroundColor: Colors.transparent,
          child: Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: themeProvider.isDarkMode ? Colors.grey[850] : Colors.white,
              borderRadius: BorderRadius.circular(24),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 20,
                  offset: const Offset(0, 10),
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // أيقونة متحركة
                AnimatedBuilder(
                  animation: _scaleAnimation,
                  builder: (context, child) {
                    return Transform.scale(
                      scale: _scaleAnimation.value,
                      child: Container(
                        width: 100,
                        height: 100,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          gradient: LinearGradient(
                            colors: _isCompleted
                                ? [Colors.green[400]!, Colors.green[600]!]
                                : [Colors.blue[400]!, Colors.blue[600]!],
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: (_isCompleted ? Colors.green : Colors.blue)
                                  .withValues(alpha: 0.3),
                              blurRadius: 20,
                              spreadRadius: 2,
                            ),
                          ],
                        ),
                        child: _isCompleted
                            ? const Icon(
                                Icons.check_rounded,
                                color: Colors.white,
                                size: 50,
                              )
                            : AnimatedBuilder(
                                animation: _rotationAnimation,
                                builder: (context, child) {
                                  return Transform.rotate(
                                    angle: _rotationAnimation.value * 2 * 3.14159,
                                    child: const Icon(
                                      Icons.download_rounded,
                                      color: Colors.white,
                                      size: 50,
                                    ),
                                  );
                                },
                              ),
                      ),
                    );
                  },
                ),
                const SizedBox(height: 24),
                
                // عنوان
                Text(
                  _isCompleted ? 'تم التحميل!' : 'جاري التحميل...',
                  style: GoogleFonts.cairo(
                    fontSize: 22,
                    fontWeight: FontWeight.w700,
                    color: themeProvider.isDarkMode ? Colors.white : Colors.grey[800],
                  ),
                ),
                const SizedBox(height: 8),
                
                // اسم الملف
                Text(
                  widget.pdf.name,
                  style: GoogleFonts.cairo(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 24),
                
                // مؤشر التقدم
                Container(
                  width: double.infinity,
                  height: 8,
                  decoration: BoxDecoration(
                    color: Colors.grey[200],
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(4),
                    child: LinearProgressIndicator(
                      value: _progress,
                      backgroundColor: Colors.transparent,
                      valueColor: AlwaysStoppedAnimation<Color>(
                        _isCompleted ? Colors.green[600]! : Colors.blue[600]!,
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 12),
                
                // نسبة التقدم
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      _statusMessage,
                      style: GoogleFonts.cairo(
                        fontSize: 12,
                        color: Colors.grey[600],
                      ),
                    ),
                    Text(
                      '${(_progress * 100).toInt()}%',
                      style: GoogleFonts.cairo(
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                        color: _isCompleted ? Colors.green[600] : Colors.blue[600],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                
                // رسالة إضافية
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: (_isCompleted ? Colors.green : Colors.blue)[50],
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: (_isCompleted ? Colors.green : Colors.blue)[200]!,
                    ),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        _isCompleted ? Icons.folder_open : Icons.info_outline,
                        color: (_isCompleted ? Colors.green : Colors.blue)[600],
                        size: 16,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          _isCompleted
                              ? 'تم حفظ الملف في مجلد التحميلات'
                              : 'يتم حفظ الملف في مجلد التحميلات',
                          style: GoogleFonts.cairo(
                            fontSize: 12,
                            color: (_isCompleted ? Colors.green : Colors.blue)[700],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
