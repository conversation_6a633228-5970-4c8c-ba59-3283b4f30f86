# Copy APK files to root with new names
Write-Host "Copying APK files..." -ForegroundColor Green

$sourceDir = "android\app\build\outputs\flutter-apk"

# Copy release APK
if (Test-Path "$sourceDir\app-release.apk") {
    Copy-Item "$sourceDir\app-release.apk" "legl92025-v1.1.0-release.apk" -Force
    $size = [math]::Round((Get-Item "legl92025-v1.1.0-release.apk").Length / 1MB, 1)
    Write-Host "Release APK: $size MB" -ForegroundColor Cyan
}

# Copy debug APK
if (Test-Path "$sourceDir\app-debug.apk") {
    Copy-Item "$sourceDir\app-debug.apk" "legl92025-v1.1.0-debug.apk" -Force
    $size = [math]::Round((Get-Item "legl92025-v1.1.0-debug.apk").Length / 1MB, 1)
    Write-Host "Debug APK: $size MB" -ForegroundColor Cyan
}

# Copy ARM64 APK
if (Test-Path "$sourceDir\app-arm64-v8a-release.apk") {
    Copy-Item "$sourceDir\app-arm64-v8a-release.apk" "legl92025-v1.1.0-arm64-v8a.apk" -Force
    $size = [math]::Round((Get-Item "legl92025-v1.1.0-arm64-v8a.apk").Length / 1MB, 1)
    Write-Host "ARM64 APK: $size MB" -ForegroundColor Cyan
}

# Copy ARMv7 APK
if (Test-Path "$sourceDir\app-armeabi-v7a-release.apk") {
    Copy-Item "$sourceDir\app-armeabi-v7a-release.apk" "legl92025-v1.1.0-armeabi-v7a.apk" -Force
    $size = [math]::Round((Get-Item "legl92025-v1.1.0-armeabi-v7a.apk").Length / 1MB, 1)
    Write-Host "ARMv7 APK: $size MB" -ForegroundColor Cyan
}

# Copy x86_64 APK
if (Test-Path "$sourceDir\app-x86_64-release.apk") {
    Copy-Item "$sourceDir\app-x86_64-release.apk" "legl92025-v1.1.0-x86_64.apk" -Force
    $size = [math]::Round((Get-Item "legl92025-v1.1.0-x86_64.apk").Length / 1MB, 1)
    Write-Host "x86_64 APK: $size MB" -ForegroundColor Cyan
}

Write-Host "All APK files copied successfully!" -ForegroundColor Green
